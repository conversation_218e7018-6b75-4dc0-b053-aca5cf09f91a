{"name": "online-voting-system-server", "version": "1.0.0", "description": "Secure server for Online Voting System with biometric verification and fraud detection", "main": "server.js", "scripts": {"start": "node simple-server.js", "dev": "nodemon simple-server.js", "secure": "node secure-server.js", "secure-dev": "nodemon secure-server.js", "mock": "node mockServer.js", "mock-dev": "nodemon mockServer.js", "seed": "node seed.js", "check": "node checkServer.js", "init-uploads": "node initUploads.js", "setup": "npm run init-uploads && npm run seed", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"@tensorflow/tfjs-node": "^4.10.0", "bcryptjs": "^2.4.3", "canvas": "^2.11.2", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.18.2", "face-api.js": "^0.22.2", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.0", "multer": "^1.4.5-lts.1", "node-tesseract-ocr": "^2.2.1", "node-wav": "^0.0.2", "winston": "^3.10.0"}, "devDependencies": {"jest": "^29.6.4", "nodemon": "^3.0.1"}, "engines": {"node": ">=14.0.0"}}